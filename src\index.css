@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap");
@import "tailwindcss";
:root {
  /* Primary Colors */
  --color-primary-base: #38e07b;
  --color-primary-hover: #2fc56c;
  --color-primary-pressed: #26a55e;
  --color-primary-disabled: #b2efc8;
  --color-primary-focus: transparent; /* Value missing, set default */

  /* Secondary Colors */
  --color-secondary-base: #264533;
  --color-secondary-hover: #315b42;
  --color-secondary-pressed: #1c3526;
  --color-secondary-disabled: #a7b6af;
  --color-secondary-focus: transparent; /* Value missing, set default */

  /* Background Colors */
  --color-bg-base: #122117;
  --color-bg-card: #366347;
  --color-bg-text: #ffffff;
  --color-bg-muted-text: #d1e3d6;
  --color-bg-divider: #5b8f72;

  /* Danger Colors */
  --color-danger-light: #e02b2b;
  --color-danger-dark: #a41616;
  --color-danger-dark-plus: #a41616;

  /* Success Colors */
  --color-success-light: #1b9e1b;
  --color-success-dark: #107c10;
  --color-success-dark-plus: #052505;

  /* Warning Colors */
  --color-warning-light: #fff9f5;
  --color-warning-dark: #f7630c;
  --color-warning-dark-plus: #bc4b09;
}

html,
body {
  background-color: var(--color-bg-base);
  font-family: "Cairo", sans-serif;
}

/* Utility classes for success and danger states */
.success {
  background-color: var(--color-success-light);
  color: var(--color-bg-text);
  cursor: pointer;
}
.success:hover {
  background-color: var(--color-success-dark);
}
.success:active {
  background-color: var(--color-success-dark-plus);
}
.success:disabled {
  background-color: var(--color-secondary-disabled);
  color: var(--color-bg-muted-text);
  cursor: not-allowed;
}
.danger {
  background-color: var(--color-danger-light);
  color: var(--color-bg-text);
  cursor: pointer;
}
.danger:hover {
  background-color: var(--color-danger-dark);
}
.danger:active {
  background-color: var(--color-danger-dark-plus);
}
.danger:disabled {
  background-color: var(--color-secondary-disabled);
  color: var(--color-bg-muted-text);
  cursor: not-allowed;
}

.logo-draw {
  stroke-dasharray: 2000;
  stroke-dashoffset: 2000;
  animation: drawLogo 3s ease forwards;
}

@keyframes drawLogo {
  to {
    stroke-dashoffset: 0;
  }
}
